---
type: "always_apply"
---

# Reglas Generales de Proyecto

## Documentacion de Proyecto

### PRD (Product Request Document)

- **Deseado**: Todo proyecto idealmente debe incluir PRD con especificaciones completas
- **Contenido**: Funcionalidad, lenguajes, aplicativos, middleware, arquitectura, dependencias
- **Ubicacion**: `PRD.md` en raiz del proyecto
- **Sin PRD**: Hacer preguntas para clarificar especificaciones antes de proceder

### Reglas Agnosticas

- Se aplican independientemente del stack tecnologico
- **Precedencia**: PRD > Rules especificas > User Guidelines
- **Adaptabilidad**: Compatible con cualquier tecnologia o Stack

## Filosofia de Desarrollo

### Principios Core

- **Matriz de prioridades**: Seguridad > Confiabilidad > Rendimiento > Simplicidad
- **Codigo limpio**: Optimizado para humanos, no maquinas
- **Modularidad**: Una responsabilidad por modulo
- **Testing continuo**: Validar despues de cada cambio significativo

### Performance vs Security Guidelines

#### Scenarios Permitidos

- **Caching sensible**: Cache datos no-criticos max 5min, datos criticos max 30s
- **Rate limiting flexibility**: Aumentar limites 20% bajo carga, nunca >50%
- **Connection pooling**: Pools mayores permitidos si monitoring activo
- **Compression trade-offs**: Reducir compresion si CPU >80%, mantener HTTPS siempre

#### Thresholds Criticos

- **Response time**: >2s justifica optimizacion, >5s requiere action inmediata
- **Memory usage**: >85% permite cache reduction, >95% requiere scaling
- **CPU usage**: >80% permite algorithm simplification con approval
- **Database**: >1000ms queries requieren optimization inmediata

#### Never Compromise

- **Authentication**: Nunca reducir validacion por performance
- **Authorization**: Checks completos siempre, cache results no decisions
- **Data encryption**: Nunca degradar encryption por speed
- **Input validation**: Validation completa obligatoria siempre
- **Audit logs**: Nunca omitir por performance, async si necesario

### Enfoque SLC (Simple, Lovable, Complete)

- **Simple**: Interfaz intuitiva sin manual de usuario
- **Lovable**: Experiencia que resuelve dolor real del usuario
- **Complete**: Funcionalidad completa, sin features "proximamente"
- **NO MVP**: Entregar valor completo desde v1.0

### Reglas de Oro

- **DRY** (Don't Repeat Yourself)
- **KISS** (Keep It Simple, Stupid)
- **YAGNI** (You Aren't Gonna Need It)
- **Security by Design**

## Estructura de Proyectos

### Estructura Universal Base

```
project-root/
├── .augment/rules/        # Reglas Augment Code
├── src/
│   ├── frontend/          # Cliente
│   ├── backend/           # Servidor/API
│   └── shared/            # Codigo compartido
├── database/              # Schemas y migraciones
├── tests/                 # Pruebas organizadas
├── docs/                  # Documentacion
├── scripts/               # Utilidades
├── config/                # Configuraciones por ambiente
└── README.md
```

### Umbrales de Escalabilidad

- **<5 features**: Estructura universal base
- **5-10 features**: Considerar modularizacion por capa
- **>10 features**: Estructura por feature obligatoria
- **<3 desarrolladores**: Estructura universal aceptable
- **>3 desarrolladores**: Modularizacion requerida
- **>100k usuarios**: Arquitectura microservicios considerar
- **>1TB datos**: Particionamiento base datos evaluar

## Jerarquia de Tareas

### Estructura 4 Niveles

```
Epica (Modulo completo)
  └── Historia Usuario (Feature)
      └── Tarea (Implementacion)
          └── Subtarea (Pasos especificos)
```

### Formato Estandar

- **Epica**: EP-XXX, modulo completo del sistema
- **Historia**: HU-XXX, Como/Quiero/Para con criterios aceptacion
- **Tarea**: T-XXX, implementacion tecnica con checklist
- **Subtarea**: ST-XXX, pasos atomicos y especificos

### Estados de Trabajo

1. Por Hacer - No iniciado
2. En Progreso - Trabajando activamente  
3. En Revision - Necesita validacion
4. Completado - Terminado y probado
5. Bloqueado - Impedimento externo

## Prioridades de Desarrollo

### Orden de Implementacion

1. **Funcionalidad core** antes que optimizacion
2. **Seguridad** integrada desde diseño
3. **User Experience** de calidad desde v1.0
4. **Documentacion** durante desarrollo, no despues
5. **Escalabilidad** considerada, no sobre-engineered

### Mentalidad de Trabajo

- **Focus unitario**: Una tarea a la vez
- **Commits atomicos**: Cambios logicos completos
- **Review personal**: Antes de considerar terminado
- **Sin placeholders**: Codigo completo y funcional
- **Iteracion rapida**: Feedback temprano y frecuente

## Principios con IA

### Metodologia de Trabajo

1. **Razonamiento previo**: Escribir analisis antes de codear
2. **Humildad tecnica**: Asumir desconocimiento al debuggear
3. **Mente abierta**: Considerar multiples causas posibles
4. **Analisis completo**: No saltar a conclusiones
5. **Seguimiento estricto**: Leer instrucciones dos veces, ejecutar una

### Criterios de Simplicidad

- **Funciones**: Maximo 3 parametros idealmente
- **Clases**: Maximo 5 metodos publicos
- **Responsabilidades**: Una principal por archivo
- **Anidamiento**: Maximo 3 niveles de condiciones

## Error Recovery Strategies

### Estrategias Recuperacion Automatica

- **Graceful Degradation**: Funcionalidad reducida vs complete failure
- **Failover**: Instancia backup automatico <5s downtime
- **Data Recovery**: Backup automatico cada 6h, retention 30 dias
- **Service Mesh**: Health checks cada 10s, auto-restart failures

### Incident Response

- **Deteccion**: Alertas automaticas <2min incident
- **Escalacion**: Critical alerts → on-call immediate
- **Communication**: Status page updated <5min
- **Resolution**: Rollback capability <10min
- **Post-mortem**: Obligatorio incidents >30min downtime
